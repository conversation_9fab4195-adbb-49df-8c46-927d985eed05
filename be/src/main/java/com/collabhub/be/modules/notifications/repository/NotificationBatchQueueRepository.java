package com.collabhub.be.modules.notifications.repository;

import com.collabhub.be.modules.notifications.constants.BatchingConstants;
import com.collabhub.be.modules.notifications.dto.BatchQueueStatus;
import com.collabhub.be.modules.notifications.dto.NotificationBatchQueueItem;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.exception.NotificationBatchingException;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.generated.tables.daos.NotificationBatchQueueDao;
import org.jooq.generated.tables.pojos.NotificationBatchQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.jooq.generated.Tables.NOTIFICATION_BATCH_QUEUE;
import static org.jooq.impl.DSL.*;

/**
 * Repository for notification batch queue operations using jOOQ for database access.
 *
 * <p>This repository provides strongly-typed, production-grade data access methods
 * for managing notification batching operations. It replaces string literals with
 * proper enums and provides comprehensive error handling and logging.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Type-safe batch queue operations using {@link BatchQueueStatus} enum</li>
 *   <li>Strongly-typed data structures with {@link NotificationBatchQueueItem}</li>
 *   <li>Comprehensive error handling with domain-specific exceptions</li>
 *   <li>Detailed logging and monitoring support</li>
 *   <li>Optimized queries with proper indexing strategies</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Repository
public class NotificationBatchQueueRepository extends NotificationBatchQueueDao {

    private static final Logger logger = LoggerFactory.getLogger(NotificationBatchQueueRepository.class);

    private final DSLContext dsl;
    private final ObjectMapper objectMapper;

    /**
     * Constructs a new NotificationBatchQueueRepository with the specified DSL context.
     *
     * @param dsl the jOOQ DSL context for database operations
     * @param objectMapper the Jackson ObjectMapper for JSON serialization/deserialization
     */
    public NotificationBatchQueueRepository(DSLContext dsl, ObjectMapper objectMapper) {
        super(dsl.configuration());
        this.dsl = dsl;
        this.objectMapper = objectMapper;
    }

    /**
     * Finds pending notifications ready for batch processing, grouped by user and batch window.
     *
     * <p>This method retrieves notifications that are ready for processing based on their
     * batch window timing and groups them by user ID and batch window start time for
     * efficient batch processing.</p>
     *
     * @param cutoffTime notifications with batch_window_start before this time are considered ready
     * @param maxBatches maximum number of batch groups to return to prevent memory issues
     * @return map where keys are batch identifiers and values are lists of ready notifications
     * @throws NotificationBatchingException if database operation fails
     *
     * @example
     * <pre>
     * LocalDateTime cutoff = LocalDateTime.now().minusMinutes(1);
     * Map&lt;String, List&lt;NotificationBatchQueue&gt;&gt; batches =
     *     repository.findPendingBatches(cutoff, 100);
     * </pre>
     */
    public Map<String, List<NotificationBatchQueue>> findPendingBatches(LocalDateTime cutoffTime, int maxBatches) {
        logger.debug("Finding pending batches with cutoff time: {} and max batches: {}", cutoffTime, maxBatches);

        try {
            Result<Record> records = dsl.select()
                    .from(NOTIFICATION_BATCH_QUEUE)
                    .where(NOTIFICATION_BATCH_QUEUE.STATUS.eq(BatchQueueStatus.PENDING.getDatabaseValue()))
                    .and(NOTIFICATION_BATCH_QUEUE.BATCH_WINDOW_START.le(cutoffTime))
                    .orderBy(NOTIFICATION_BATCH_QUEUE.BATCH_WINDOW_START.asc(),
                            NOTIFICATION_BATCH_QUEUE.CREATED_AT.asc())
                    .limit(maxBatches * BatchingConstants.Limits.MAX_NOTIFICATIONS_PER_BATCH)
                    .fetch();

            List<NotificationBatchQueue> pendingNotifications = records.stream()
                    .map(record -> record.into(NotificationBatchQueue.class))
                    .toList();

            // Group by user_id and batch_window_start for batch processing
            Map<String, List<NotificationBatchQueue>> groupedBatches = pendingNotifications.stream()
                    .collect(Collectors.groupingBy(this::generateBatchKey));

            logger.info("BATCH DISCOVERY: Found {} pending notifications grouped into {} batches with cutoff_time={}",
                       pendingNotifications.size(), groupedBatches.size(), cutoffTime);

            // Log each batch for debugging
            for (Map.Entry<String, List<NotificationBatchQueue>> entry : groupedBatches.entrySet()) {
                String batchKey = entry.getKey();
                List<NotificationBatchQueue> batchNotifications = entry.getValue();
                String notificationIds = batchNotifications.stream()
                        .map(n -> n.getId().toString())
                        .collect(Collectors.joining(", "));
                logger.debug("BATCH DETAILS: batch_key={}, notification_count={}, notification_ids=[{}]",
                           batchKey, batchNotifications.size(), notificationIds);
            }

            return groupedBatches;

        } catch (Exception e) {
            logger.error("Failed to find pending batches", e);
            throw new NotificationBatchingException("Failed to retrieve pending batches from database", e);
        }
    }

    /**
     * Finds notifications that are eligible for retry processing.
     *
     * <p>This method identifies failed notifications that have not exceeded the maximum
     * retry attempts and are past their retry delay period, making them eligible for
     * another processing attempt.</p>
     *
     * @param retryAfter notifications with last_retry_at before this time are ready for retry
     * @param maxRetryAttempts maximum retry attempts allowed before giving up
     * @return list of failed notifications ready for retry, ordered by creation time
     * @throws NotificationBatchingException if database operation fails
     */
    public List<NotificationBatchQueue> findRetryableNotifications(LocalDateTime retryAfter, int maxRetryAttempts) {
        logger.debug("Finding retryable notifications with retry after: {} and max attempts: {}",
                    retryAfter, maxRetryAttempts);

        try {
            Result<Record> records = dsl.select()
                    .from(NOTIFICATION_BATCH_QUEUE)
                    .where(NOTIFICATION_BATCH_QUEUE.STATUS.eq(BatchQueueStatus.FAILED.getDatabaseValue()))
                    .and(NOTIFICATION_BATCH_QUEUE.RETRY_COUNT.lt(maxRetryAttempts))
                    .and(NOTIFICATION_BATCH_QUEUE.LAST_RETRY_AT.isNull()
                         .or(NOTIFICATION_BATCH_QUEUE.LAST_RETRY_AT.le(retryAfter)))
                    .orderBy(NOTIFICATION_BATCH_QUEUE.CREATED_AT.asc())
                    .limit(BatchingConstants.Limits.MAX_NOTIFICATIONS_PER_QUERY)
                    .fetch();

            List<NotificationBatchQueue> retryableNotifications = records.stream()
                    .map(record -> record.into(NotificationBatchQueue.class))
                    .collect(Collectors.toList());

            logger.info("Found {} notifications eligible for retry", retryableNotifications.size());
            return retryableNotifications;

        } catch (Exception e) {
            logger.error("Failed to find retryable notifications", e);
            throw new NotificationBatchingException("Failed to retrieve retryable notifications from database", e);
        }
    }

    /**
     * Marks notifications as currently being processed to prevent duplicate processing.
     *
     * <p>This method atomically updates notification status from PENDING to PROCESSING,
     * ensuring that only one processor instance can work on a batch at a time. The
     * operation only affects notifications that are currently in PENDING status.</p>
     *
     * @param notificationIds list of notification IDs to mark as processing
     * @return number of notifications successfully updated to PROCESSING status
     * @throws NotificationBatchingException if database operation fails
     * @throws IllegalArgumentException if notificationIds is null
     */
    public int markAsProcessing(List<Long> notificationIds) {
        if (notificationIds == null) {
            throw new IllegalArgumentException("Notification IDs list cannot be null");
        }

        if (notificationIds.isEmpty()) {
            logger.debug("No notification IDs provided for processing status update");
            return 0;
        }

        logger.debug("Marking {} notifications as processing", notificationIds.size());

        try {
            int updatedCount = dsl.update(NOTIFICATION_BATCH_QUEUE)
                    .set(NOTIFICATION_BATCH_QUEUE.STATUS, BatchQueueStatus.PROCESSING.getDatabaseValue())
                    .where(NOTIFICATION_BATCH_QUEUE.ID.in(notificationIds))
                    .and(NOTIFICATION_BATCH_QUEUE.STATUS.eq(BatchQueueStatus.PENDING.getDatabaseValue()))
                    .execute();

            logger.info("Successfully marked {} out of {} notifications as processing",
                       updatedCount, notificationIds.size());

            return updatedCount;

        } catch (Exception e) {
            logger.error("Failed to mark notifications as processing: {}", notificationIds, e);
            throw new NotificationBatchingException("Failed to update notification status to PROCESSING", e);
        }
    }

    /**
     * Marks notifications as successfully sent and processed.
     *
     * <p>This method updates notification status to SENT and sets the processed_at
     * timestamp, indicating successful completion of the batch processing pipeline.</p>
     *
     * @param notificationIds list of notification IDs to mark as sent
     * @return number of notifications successfully updated to SENT status
     * @throws NotificationBatchingException if database operation fails
     * @throws IllegalArgumentException if notificationIds is null
     */
    public int markAsSent(List<Long> notificationIds) {
        if (notificationIds == null) {
            throw new IllegalArgumentException("Notification IDs list cannot be null");
        }

        if (notificationIds.isEmpty()) {
            logger.debug("No notification IDs provided for sent status update");
            return 0;
        }

        logger.debug("Marking {} notifications as sent", notificationIds.size());

        try {
            LocalDateTime now = LocalDateTime.now();
            int updatedCount = dsl.update(NOTIFICATION_BATCH_QUEUE)
                    .set(NOTIFICATION_BATCH_QUEUE.STATUS, BatchQueueStatus.SENT.getDatabaseValue())
                    .set(NOTIFICATION_BATCH_QUEUE.PROCESSED_AT, now)
                    .where(NOTIFICATION_BATCH_QUEUE.ID.in(notificationIds))
                    .execute();

            logger.info("Successfully marked {} notifications as sent", updatedCount);
            return updatedCount;

        } catch (Exception e) {
            logger.error("Failed to mark notifications as sent: {}", notificationIds, e);
            throw new NotificationBatchingException("Failed to update notification status to SENT", e);
        }
    }

    /**
     * Marks notifications as failed, increments retry count, and stores error information.
     *
     * <p>This method updates notification status to FAILED, increments the retry count,
     * records the failure timestamp, and stores the error message for debugging purposes.
     * The error message is truncated if it exceeds the maximum allowed length.</p>
     *
     * @param notificationIds list of notification IDs to mark as failed
     * @param errorMessage error message to store (will be truncated if too long)
     * @return number of notifications successfully updated to FAILED status
     * @throws NotificationBatchingException if database operation fails
     * @throws IllegalArgumentException if notificationIds is null
     */
    public int markAsFailed(List<Long> notificationIds, String errorMessage) {
        if (notificationIds == null) {
            throw new IllegalArgumentException("Notification IDs list cannot be null");
        }

        if (notificationIds.isEmpty()) {
            logger.debug("No notification IDs provided for failed status update");
            return 0;
        }

        logger.debug("Marking {} notifications as failed with error: {}",
                    notificationIds.size(), errorMessage);

        try {
            // Truncate error message if too long
            String truncatedErrorMessage = truncateErrorMessage(errorMessage);

            LocalDateTime now = LocalDateTime.now();
            int updatedCount = dsl.update(NOTIFICATION_BATCH_QUEUE)
                    .set(NOTIFICATION_BATCH_QUEUE.STATUS, BatchQueueStatus.FAILED.getDatabaseValue())
                    .set(NOTIFICATION_BATCH_QUEUE.RETRY_COUNT, NOTIFICATION_BATCH_QUEUE.RETRY_COUNT.plus(1))
                    .set(NOTIFICATION_BATCH_QUEUE.LAST_RETRY_AT, now)
                    .set(NOTIFICATION_BATCH_QUEUE.ERROR_MESSAGE, truncatedErrorMessage)
                    .where(NOTIFICATION_BATCH_QUEUE.ID.in(notificationIds))
                    .execute();

            logger.warn("Marked {} notifications as failed due to: {}", updatedCount, truncatedErrorMessage);
            return updatedCount;

        } catch (Exception e) {
            logger.error("Failed to mark notifications as failed: {}", notificationIds, e);
            throw new NotificationBatchingException("Failed to update notification status to FAILED", e);
        }
    }

    /**
     * Resets failed notifications back to pending status for retry processing.
     *
     * <p>This method is used to reset notifications from FAILED status back to PENDING,
     * allowing them to be picked up for retry processing. This is typically called
     * after the retry delay period has elapsed.</p>
     *
     * @param notificationIds list of notification IDs to reset to pending status
     * @return number of notifications successfully reset to PENDING status
     * @throws NotificationBatchingException if database operation fails
     * @throws IllegalArgumentException if notificationIds is null
     */
    public int resetToPending(List<Long> notificationIds) {
        if (notificationIds == null) {
            throw new IllegalArgumentException("Notification IDs list cannot be null");
        }

        if (notificationIds.isEmpty()) {
            logger.debug("No notification IDs provided for pending status reset");
            return 0;
        }

        logger.debug("Resetting {} notifications to pending status for retry", notificationIds.size());

        try {
            int updatedCount = dsl.update(NOTIFICATION_BATCH_QUEUE)
                    .set(NOTIFICATION_BATCH_QUEUE.STATUS, BatchQueueStatus.PENDING.getDatabaseValue())
                    .where(NOTIFICATION_BATCH_QUEUE.ID.in(notificationIds))
                    .execute();

            logger.info("Successfully reset {} notifications to pending status", updatedCount);
            return updatedCount;

        } catch (Exception e) {
            logger.error("Failed to reset notifications to pending: {}", notificationIds, e);
            throw new NotificationBatchingException("Failed to reset notification status to PENDING", e);
        }
    }

    /**
     * Cleans up old processed notifications to prevent database bloat and maintain performance.
     *
     * <p>This method removes notifications that have been successfully processed or have
     * permanently failed and are older than the specified cutoff time. It processes
     * deletions in batches to avoid long-running transactions and database locks.</p>
     *
     * @param cutoffTime notifications processed before this time will be deleted
     * @param batchSize maximum number of notifications to delete in one operation
     * @return number of notifications successfully deleted
     * @throws NotificationBatchingException if database operation fails
     * @throws IllegalArgumentException if cutoffTime is null or batchSize is invalid
     */
    public int cleanupProcessedNotifications(LocalDateTime cutoffTime, int batchSize) {
        if (cutoffTime == null) {
            throw new IllegalArgumentException("Cutoff time cannot be null");
        }

        if (batchSize <= 0) {
            throw new IllegalArgumentException("Batch size must be positive");
        }

        logger.debug("Cleaning up processed notifications older than {} in batches of {}",
                    cutoffTime, batchSize);

        try {
            int deletedCount = dsl.deleteFrom(NOTIFICATION_BATCH_QUEUE)
                    .where(NOTIFICATION_BATCH_QUEUE.STATUS.in(
                        BatchQueueStatus.SENT.getDatabaseValue(),
                        BatchQueueStatus.FAILED.getDatabaseValue()))
                    .and(NOTIFICATION_BATCH_QUEUE.PROCESSED_AT.isNotNull())
                    .and(NOTIFICATION_BATCH_QUEUE.PROCESSED_AT.lt(cutoffTime))
                    .limit(batchSize)
                    .execute();

            if (deletedCount > 0) {
                logger.info("Cleaned up {} processed notifications older than {}", deletedCount, cutoffTime);
            } else {
                logger.debug("No processed notifications found for cleanup");
            }

            return deletedCount;

        } catch (Exception e) {
            logger.error("Failed to cleanup processed notifications", e);
            throw new NotificationBatchingException("Failed to cleanup processed notifications", e);
        }
    }

    /**
     * Gets comprehensive statistics about the batch queue for monitoring and observability.
     *
     * <p>This method provides detailed statistics about the current state of the batch
     * queue, including counts by status, which can be used for monitoring, alerting,
     * and capacity planning purposes.</p>
     *
     * @return map where keys are status names and values are notification counts
     * @throws NotificationBatchingException if database operation fails
     */
    public Map<String, Integer> getBatchQueueStatistics() {
        logger.debug("Retrieving batch queue statistics");

        try {
            // Get counts by status using jOOQ aggregation
            var result = dsl.select(
                    NOTIFICATION_BATCH_QUEUE.STATUS,
                    org.jooq.impl.DSL.count().as("count")
                )
                .from(NOTIFICATION_BATCH_QUEUE)
                .groupBy(NOTIFICATION_BATCH_QUEUE.STATUS)
                .fetch();

            // Convert to map with status names as keys
            Map<String, Integer> statistics = result.stream()
                .collect(Collectors.toMap(
                    record -> record.get(NOTIFICATION_BATCH_QUEUE.STATUS).toString(),
                    record -> record.get("count", Integer.class)
                ));

            // Add total count
            int totalCount = statistics.values().stream().mapToInt(Integer::intValue).sum();
            statistics.put("TOTAL", totalCount);

            logger.debug("Retrieved batch queue statistics: {}", statistics);
            return statistics;

        } catch (Exception e) {
            logger.error("Failed to retrieve batch queue statistics", e);
            throw new NotificationBatchingException("Failed to retrieve batch queue statistics", e);
        }
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Maps a database record to a NotificationBatchQueueItem.
     *
     * @param record the database record to map
     * @return the mapped NotificationBatchQueueItem
     */
    private NotificationBatchQueueItem mapRecordToQueueItem(Record record) {
        return NotificationBatchQueueItem.builder()
                .id(record.get(NOTIFICATION_BATCH_QUEUE.ID))
                .userId(record.get(NOTIFICATION_BATCH_QUEUE.USER_ID))
                .notificationType(NotificationType.valueOf(
                    record.get(NOTIFICATION_BATCH_QUEUE.NOTIFICATION_TYPE).name()))
                .urgency(NotificationUrgency.fromJooqEnum(
                    record.get(NOTIFICATION_BATCH_QUEUE.URGENCY)))
                .title(record.get(NOTIFICATION_BATCH_QUEUE.TITLE))
                .message(record.get(NOTIFICATION_BATCH_QUEUE.MESSAGE))
                .status(BatchQueueStatus.fromDatabaseValue(
                    record.get(NOTIFICATION_BATCH_QUEUE.STATUS)))
                .batchWindowStart(record.get(NOTIFICATION_BATCH_QUEUE.BATCH_WINDOW_START))
                .metadata(parseMetadata(record.get(NOTIFICATION_BATCH_QUEUE.METADATA)))
                .entityReferences(createEntityReferences(record))
                .retryCount(record.get(NOTIFICATION_BATCH_QUEUE.RETRY_COUNT))
                .lastRetryAt(record.get(NOTIFICATION_BATCH_QUEUE.LAST_RETRY_AT))
                .errorMessage(record.get(NOTIFICATION_BATCH_QUEUE.ERROR_MESSAGE))
                .createdAt(record.get(NOTIFICATION_BATCH_QUEUE.CREATED_AT))
                .processedAt(record.get(NOTIFICATION_BATCH_QUEUE.PROCESSED_AT))
                .build();
    }

    /**
     * Generates a batch key for grouping notifications by recipient and batch window.
     *
     * <p>This method creates a consistent batch key for both internal users (user_id)
     * and external participants (email), enabling unified batch processing.</p>
     *
     * @param notification the notification to generate a key for
     * @return the batch key in format "user_{userId}_{timestamp}" or "email_{email}_{timestamp}"
     */
    private String generateBatchKey(NotificationBatchQueue notification) {
        String recipientIdentifier;
        String recipientType;

        if (notification.getUserId() != null) {
            recipientType = "user";
            recipientIdentifier = notification.getUserId().toString();
        } else if (notification.getEmail() != null) {
            recipientType = "email";
            recipientIdentifier = notification.getEmail().toLowerCase().trim();
        } else {
            throw new IllegalStateException("Notification must have either userId or email: " + notification.getId());
        }

        String batchKey = String.format("%s_%s_%s",
                                       recipientType,
                                       recipientIdentifier,
                                       notification.getBatchWindowStart().toString());

        logger.debug("Generated batch key: {} for notification {} (type: {}, urgency: {}, window: {})",
                    batchKey, notification.getId(), notification.getNotificationType(),
                    notification.getUrgency(), notification.getBatchWindowStart());

        return batchKey;
    }

    /**
     * Truncates error message to fit database constraints.
     *
     * @param errorMessage the error message to truncate
     * @return the truncated error message or null if input was null
     */
    private String truncateErrorMessage(String errorMessage) {
        if (errorMessage == null) {
            return null;
        }

        if (errorMessage.length() <= BatchingConstants.Limits.MAX_ERROR_MESSAGE_LENGTH) {
            return errorMessage;
        }

        return errorMessage.substring(0, BatchingConstants.Limits.MAX_ERROR_MESSAGE_LENGTH - 3) + "...";
    }

    /**
     * Parses metadata from database JSONB format.
     *
     * @param metadataJson the JSONB metadata from database
     * @return parsed NotificationMetadata or null if no metadata
     */
    private NotificationMetadata parseMetadata(Object metadataJson) {
        if (metadataJson == null) {
            return NotificationMetadata.empty();
        }

        try {
            String jsonString = metadataJson.toString();
            if (jsonString.trim().isEmpty() || "null".equals(jsonString)) {
                return NotificationMetadata.empty();
            }

            return objectMapper.readValue(jsonString, NotificationMetadata.class);
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse NotificationMetadata from JSONB: {}", e.getMessage());
            return NotificationMetadata.empty();
        }
    }

    /**
     * Creates entity references from database record.
     *
     * @param record the database record
     * @return EntityReferences object or null if no references
     */
    private NotificationBatchQueueItem.EntityReferences createEntityReferences(Record record) {
        Long hubId = record.get(NOTIFICATION_BATCH_QUEUE.COLLABORATION_HUB_ID);
        Long postId = record.get(NOTIFICATION_BATCH_QUEUE.POST_ID);
        Long commentId = record.get(NOTIFICATION_BATCH_QUEUE.COMMENT_ID);
        Long chatChannelId = record.get(NOTIFICATION_BATCH_QUEUE.CHAT_CHANNEL_ID);
        Long briefId = record.get(NOTIFICATION_BATCH_QUEUE.BRIEF_ID);

        // Return null if no references are present
        if (hubId == null && postId == null && commentId == null &&
            chatChannelId == null && briefId == null) {
            return null;
        }

        return new NotificationBatchQueueItem.EntityReferences(
            hubId, postId, commentId, chatChannelId, briefId);
    }

    // ========================================
    // EXTERNAL USER (EMAIL-BASED) METHODS
    // ========================================

    /**
     * Finds pending batch queue entries for external users by email.
     *
     * @param email the email address (must be valid)
     * @param batchWindowStart the batch window start time
     * @return list of pending entries for the external user
     *
     * @throws IllegalArgumentException if parameters are invalid
     * @throws NotificationBatchingException if database operation fails
     */
    public List<NotificationBatchQueue> findPendingByEmail(String email, LocalDateTime batchWindowStart) {
        validateEmailParameter(email);

        try {
            List<NotificationBatchQueue> entries = dsl.selectFrom(NOTIFICATION_BATCH_QUEUE)
                    .where(NOTIFICATION_BATCH_QUEUE.EMAIL.eq(email.toLowerCase().trim()))
                    .and(NOTIFICATION_BATCH_QUEUE.BATCH_WINDOW_START.eq(batchWindowStart))
                    .and(NOTIFICATION_BATCH_QUEUE.STATUS.eq(BatchQueueStatus.PENDING.name()))
                    .orderBy(NOTIFICATION_BATCH_QUEUE.CREATED_AT.asc())
                    .fetchInto(NotificationBatchQueue.class);

            logger.debug("Found {} pending batch entries for external user {} in window {}",
                        entries.size(), email, batchWindowStart);

            return entries;

        } catch (Exception e) {
            logger.error("Failed to find pending batch entries for external user {}: {}", email, e.getMessage(), e);
            throw new NotificationBatchingException("Failed to query batch queue for external user", e);
        }
    }

    /**
     * Validates email parameter.
     */
    private void validateEmailParameter(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or empty");
        }
        if (!email.contains("@") || !email.contains(".")) {
            throw new IllegalArgumentException("Invalid email format: " + email);
        }
        if (email.length() > 255) {
            throw new IllegalArgumentException("Email exceeds maximum length of 255 characters");
        }
    }
}
